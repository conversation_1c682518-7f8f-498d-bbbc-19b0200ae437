#!/usr/bin/env python3
"""
使用系统OpenSSL进行AES解密
"""

import subprocess
import base64
import os
import hashlib
import hmac
from struct import pack
import tempfile

class OpenSSLDecryptor:
    def __init__(self):
        self.POSSIBLE_SALTS = [
            b"MtInstaller",
            b"MaiTian", 
            b"salt",
            b"MtInstaller2023",
            b"BeautyBox",
            b"MaiTianFxPlugin"
        ]
        
        self.POSSIBLE_ITERATIONS = [1000, 4096, 10000]
        self.MACHINE_SERIAL = "C02PWRY5FR1M"
        
        # 真实的激活码
        self.AUTH_CODE = "25f8af52740a429682dbc52a9be0ca48"
        
        # 可能的产品码
        self.POSSIBLE_PRODUCT_CODES = [
            "",
            "BeautyBox",
            "MaiTian",
            "MtInstaller", 
            "FxPlug",
            "6.0",
            "BeautyBox6.0"
        ]
    
    def pbkdf2_derive_key(self, password, salt, iterations, key_length):
        """使用系统PBKDF2派生密钥"""
        def prf(password, salt):
            return hmac.new(password, salt, hashlib.sha256).digest()
        
        def f(password, salt, iterations, i):
            u = prf(password, salt + pack('>I', i))
            result = u
            for _ in range(iterations - 1):
                u = prf(password, u)
                result = bytes(a ^ b for a, b in zip(result, u))
            return result
        
        key = b''
        i = 1
        while len(key) < key_length:
            key += f(password, salt, iterations, i)
            i += 1
        
        return key[:key_length]
    
    def openssl_aes_decrypt(self, data, key, iv=None):
        """使用OpenSSL进行AES解密"""
        if iv is None:
            # 尝试使用数据的前16字节作为IV
            if len(data) >= 32:
                iv = data[:16]
                ciphertext = data[16:]
            else:
                iv = b'\x00' * 16
                ciphertext = data
        else:
            ciphertext = data
        
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False) as key_file:
                key_file.write(key)
                key_file_path = key_file.name
            
            with tempfile.NamedTemporaryFile(delete=False) as iv_file:
                iv_file.write(iv)
                iv_file_path = iv_file.name
            
            with tempfile.NamedTemporaryFile(delete=False) as data_file:
                data_file.write(ciphertext)
                data_file_path = data_file.name
            
            # 使用OpenSSL解密
            cmd = [
                'openssl', 'enc', '-aes-256-cbc', '-d',
                '-K', key.hex(),
                '-iv', iv.hex(),
                '-in', data_file_path
            ]
            
            result = subprocess.run(cmd, capture_output=True)
            
            # 清理临时文件
            os.unlink(key_file_path)
            os.unlink(iv_file_path)
            os.unlink(data_file_path)
            
            if result.returncode == 0:
                return result.stdout
            else:
                return None
                
        except Exception as e:
            print(f"OpenSSL解密失败: {e}")
            return None
    
    def analyze_result(self, data, description):
        """分析解密结果"""
        if not data or len(data) < 4:
            return False
        
        print(f"  {description}:")
        print(f"    大小: {len(data)} 字节")
        
        # 显示头部
        header = data[:min(64, len(data))]
        print(f"    头部: {header.hex()}")
        
        # 检查文件类型
        success = False
        
        if data.startswith(b'xar!'):
            print(f"    ✅ XAR/PKG文件格式！")
            success = True
        elif data.startswith(b'PK'):
            print(f"    ✅ ZIP/PKG文件格式！")
            success = True
        elif data.startswith(b'\x7fELF'):
            print(f"    ✅ ELF可执行文件")
            success = True
        elif data.startswith(b'\xcf\xfa\xed\xfe') or data.startswith(b'\xfe\xed\xfa\xcf'):
            print(f"    ✅ Mach-O可执行文件")
            success = True
        elif data.startswith(b'<?xml'):
            print(f"    ✅ XML文件")
            success = True
        elif b'Contents' in data[:200] or b'Info.plist' in data[:200]:
            print(f"    ✅ 可能是应用程序包")
            success = True
        else:
            # 检查是否包含可读文本
            try:
                text_sample = data[:200].decode('utf-8', errors='ignore')
                printable_count = sum(1 for c in text_sample if c.isprintable())
                if printable_count > len(text_sample) * 0.7:
                    print(f"    ? 可能是文本文件")
                    print(f"    内容预览: {text_sample[:100]}...")
                    success = True
            except:
                pass
        
        if not success:
            print(f"    ❌ 未识别的格式")
        
        return success
    
    def comprehensive_decrypt(self):
        """全面解密测试"""
        print(f"=== OpenSSL AES解密测试 ===")
        print(f"激活码: {self.AUTH_CODE}")
        print(f"机器序列号: {self.MACHINE_SERIAL}")
        
        # 读取文件
        dat_path = "/Users/<USER>/Desktop/dat"
        prd_path = "/Users/<USER>/Desktop/prd"
        
        with open(dat_path, 'rb') as f:
            dat_raw = f.read()
        
        with open(prd_path, 'rb') as f:
            prd_data = f.read()
        
        # Base64解码dat文件
        try:
            dat_decoded = base64.b64decode(dat_raw)
            print(f"DAT文件: {len(dat_raw)} 字节 -> Base64解码后: {len(dat_decoded)} 字节")
        except:
            dat_decoded = dat_raw
            print(f"DAT文件: {len(dat_raw)} 字节 (非Base64)")
        
        print(f"PRD文件: {len(prd_data)} 字节")
        print(f"PRD内容: {prd_data.hex()}")
        
        success_count = 0
        
        # 尝试不同的产品码
        for product_code in self.POSSIBLE_PRODUCT_CODES:
            print(f"\n--- 测试产品码: '{product_code}' ---")
            
            for salt in self.POSSIBLE_SALTS:
                for iterations in self.POSSIBLE_ITERATIONS:
                    print(f"\n参数: 盐值={salt.decode('utf-8', errors='ignore')}, 迭代={iterations}")
                    
                    # 生成密钥
                    password = f"{self.AUTH_CODE}{product_code}{self.MACHINE_SERIAL}".encode('utf-8')
                    key = self.pbkdf2_derive_key(password, salt, iterations, 32)
                    
                    print(f"密钥: {key.hex()[:32]}...{key.hex()[-8:]}")
                    
                    # 方法1: 使用PRD前16字节作为IV
                    if len(prd_data) >= 16:
                        iv = prd_data[:16]
                        result = self.openssl_aes_decrypt(dat_decoded, key, iv)
                        
                        if result and self.analyze_result(result, "AES-CBC (PRD作为IV)"):
                            success_count += 1
                            self.save_result(dat_path, result, f"aes_prd_iv_{product_code}_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                            if len(result) > 1000:  # 如果结果足够大，可能是PKG文件
                                print(f"🎉 找到大文件！可能是PKG文件。")
                                return True
                    
                    # 方法2: 使用密钥前16字节作为IV
                    iv = key[:16]
                    result = self.openssl_aes_decrypt(dat_decoded, key, iv)
                    
                    if result and self.analyze_result(result, "AES-CBC (密钥作为IV)"):
                        success_count += 1
                        self.save_result(dat_path, result, f"aes_key_iv_{product_code}_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                        if len(result) > 1000:
                            print(f"🎉 找到大文件！可能是PKG文件。")
                            return True
                    
                    # 方法3: 使用零IV
                    iv = b'\x00' * 16
                    result = self.openssl_aes_decrypt(dat_decoded, key, iv)
                    
                    if result and self.analyze_result(result, "AES-CBC (零IV)"):
                        success_count += 1
                        self.save_result(dat_path, result, f"aes_zero_iv_{product_code}_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                        if len(result) > 1000:
                            print(f"🎉 找到大文件！可能是PKG文件。")
                            return True
                    
                    # 方法4: 假设前16字节是IV
                    if len(dat_decoded) >= 32:
                        iv = dat_decoded[:16]
                        ciphertext = dat_decoded[16:]
                        result = self.openssl_aes_decrypt(ciphertext, key, iv)
                        
                        if result and self.analyze_result(result, "AES-CBC (数据前16字节作为IV)"):
                            success_count += 1
                            self.save_result(dat_path, result, f"aes_data_iv_{product_code}_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                            if len(result) > 1000:
                                print(f"🎉 找到大文件！可能是PKG文件。")
                                return True
        
        print(f"\n总计成功解密: {success_count} 次")
        return success_count > 0
    
    def save_result(self, original_file, data, suffix):
        """保存解密结果"""
        output_file = f"{original_file}.openssl_{suffix}"
        try:
            with open(output_file, 'wb') as f:
                f.write(data)
            print(f"    💾 保存到: {output_file}")
        except Exception as e:
            print(f"    ❌ 保存失败: {e}")

def main():
    """主函数"""
    decryptor = OpenSSLDecryptor()
    
    print("MtInstaller OpenSSL AES解密工具")
    print("使用系统OpenSSL进行真正的AES解密")
    print("=" * 50)
    
    # 检查OpenSSL是否可用
    try:
        result = subprocess.run(['openssl', 'version'], capture_output=True, text=True)
        print(f"OpenSSL版本: {result.stdout.strip()}")
    except:
        print("❌ OpenSSL不可用，请安装OpenSSL")
        return
    
    # 开始解密
    success = decryptor.comprehensive_decrypt()
    
    if success:
        print(f"\n🎉 解密成功！")
    else:
        print(f"\n😞 未找到PKG文件。")

if __name__ == "__main__":
    main()
