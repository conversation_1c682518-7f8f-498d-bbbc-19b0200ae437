#!/usr/bin/env python3
"""
MtInstaller 解密工具
基于二进制分析推断的参数
"""

import hashlib
import base64
from Crypto.Cipher import AES
from Crypto.Protocol.KDF import PBKDF2
from Crypto.Util.Padding import unpad
import os

class MtDecryptor:
    def __init__(self):
        # 基于二进制分析得到的参数
        self.PBKDF2_SALT = b"MtInstaller"  # 在0x000129c6位置发现
        self.PBKDF2_ITERATIONS = 10000  # 在0x0001ff18位置发现
        self.AES_KEY_SIZE = 32  # AES256 = 32字节
        self.AES_IV_SIZE = 16   # AES块大小 = 16字节

        # 从二进制中发现的标记
        self.BEGIN_KEY_MARKER = b"-----BEGIN KEY-----"  # 在0x00012980
        self.END_KEY_MARKER = b"-----END KEY-----"      # 在0x000129a0

        # 可能的盐值变体
        self.POSSIBLE_SALTS = [
            b"MtInstaller",
            b"MaiTian",
            b"salt",
            b"MtInstaller2023",
            b"BeautyBox",
            b"MaiTianFxPlugin"
        ]

        # 可能的迭代次数
        self.POSSIBLE_ITERATIONS = [1000, 4096, 10000]
    
    def get_machine_serial(self):
        """获取机器序列号"""
        try:
            import subprocess
            result = subprocess.run([
                'system_profiler', 'SPHardwareDataType'
            ], capture_output=True, text=True)
            
            for line in result.stdout.split('\n'):
                if 'Serial Number' in line:
                    return line.split(':')[-1].strip()
        except:
            pass
        return "UNKNOWN_SERIAL"
    
    def generate_key(self, auth_code, product_code, serial_number=None):
        """生成解密密钥"""
        if serial_number is None:
            serial_number = self.get_machine_serial()
        
        # 组合密码材料
        password = f"{auth_code}{product_code}{serial_number}".encode('utf-8')
        
        # 使用PBKDF2派生密钥
        key = PBKDF2(
            password, 
            self.PBKDF2_SALT, 
            self.AES_KEY_SIZE,
            count=self.PBKDF2_ITERATIONS,
            hmac_hash_module=hashlib.sha256
        )
        
        return key
    
    def extract_encrypted_data(self, file_path):
        """从文件中提取加密数据"""
        with open(file_path, 'rb') as f:
            data = f.read()
        
        # 检查是否是Base64编码
        try:
            # 尝试Base64解码
            decoded = base64.b64decode(data)
            return decoded
        except:
            # 如果不是Base64，直接返回原始数据
            return data
    
    def decrypt_file(self, encrypted_file_path, key, output_path=None):
        """解密文件"""
        try:
            # 读取加密数据
            encrypted_data = self.extract_encrypted_data(encrypted_file_path)
            
            # 提取IV（假设IV在数据开头）
            iv = encrypted_data[:self.AES_IV_SIZE]
            ciphertext = encrypted_data[self.AES_IV_SIZE:]
            
            # 创建AES解密器
            cipher = AES.new(key, AES.MODE_CBC, iv)
            
            # 解密数据
            decrypted_data = cipher.decrypt(ciphertext)
            
            # 去除PKCS7填充
            decrypted_data = unpad(decrypted_data, AES.block_size)
            
            # 保存解密结果
            if output_path:
                with open(output_path, 'wb') as f:
                    f.write(decrypted_data)
                print(f"解密成功，保存到: {output_path}")
            
            return decrypted_data
            
        except Exception as e:
            print(f"解密失败: {e}")
            return None
    
    def try_all_combinations(self, encrypted_file, auth_code, product_code):
        """尝试所有可能的参数组合解密"""
        print(f"尝试解密文件: {encrypted_file}")
        print(f"授权码: {auth_code}")
        print(f"产品码: {product_code}")
        print("=" * 50)

        serial_number = self.get_machine_serial()
        print(f"机器序列号: {serial_number}")

        success_count = 0

        # 尝试不同的盐值和迭代次数组合
        for salt in self.POSSIBLE_SALTS:
            for iterations in self.POSSIBLE_ITERATIONS:
                print(f"\n尝试参数: 盐值={salt.decode('utf-8', errors='ignore')}, 迭代次数={iterations}")

                try:
                    # 临时设置参数
                    original_salt = self.PBKDF2_SALT
                    original_iterations = self.PBKDF2_ITERATIONS

                    self.PBKDF2_SALT = salt
                    self.PBKDF2_ITERATIONS = iterations

                    # 生成密钥
                    key = self.generate_key(auth_code, product_code, serial_number)
                    print(f"  生成的密钥: {key.hex()[:32]}...")

                    # 尝试解密
                    result = self.decrypt_file(encrypted_file, key)

                    if result:
                        success_count += 1
                        output_file = f"{encrypted_file}.decrypted_{salt.decode('utf-8', errors='ignore')}_{iterations}"

                        with open(output_file, 'wb') as f:
                            f.write(result)

                        print(f"  ✓ 解密成功！保存到: {output_file}")

                        # 分析文件类型
                        self.analyze_decrypted_file(result)

                    else:
                        print(f"  ✗ 解密失败")

                    # 恢复原始参数
                    self.PBKDF2_SALT = original_salt
                    self.PBKDF2_ITERATIONS = original_iterations

                except Exception as e:
                    print(f"  ✗ 解密出错: {e}")

        print(f"\n总计成功解密: {success_count} 次")
        return success_count > 0

    def analyze_decrypted_file(self, data):
        """分析解密后的文件类型"""
        if len(data) < 16:
            print("    文件太小，无法分析")
            return

        header = data[:16]
        print(f"    文件头: {header.hex()}")

        # 检查常见文件类型
        if data.startswith(b'PK'):
            print("    可能是ZIP/PKG文件")
        elif data.startswith(b'\x7fELF'):
            print("    可能是ELF可执行文件")
        elif data.startswith(b'\xcf\xfa\xed\xfe') or data.startswith(b'\xfe\xed\xfa\xcf'):
            print("    可能是Mach-O可执行文件")
        elif data.startswith(b'<?xml'):
            print("    可能是XML文件")
        elif data.startswith(b'<!DOCTYPE'):
            print("    可能是HTML文件")
        elif data.startswith(b'\x89PNG'):
            print("    可能是PNG图片")
        elif data.startswith(b'\xff\xd8\xff'):
            print("    可能是JPEG图片")
        elif b'Contents/Info.plist' in data[:1024]:
            print("    可能是macOS应用程序包")
        else:
            # 检查是否是文本文件
            try:
                text_sample = data[:100].decode('utf-8')
                print(f"    可能是文本文件，内容预览: {text_sample[:50]}...")
            except:
                print("    未知的二进制文件格式")

def main():
    """主函数 - 使用示例"""
    decryptor = MtDecryptor()

    print("MtInstaller 解密工具")
    print("基于二进制分析的智能解密")
    print("=" * 50)

    # 获取用户输入
    auth_code = input("请输入授权码: ").strip()
    product_code = input("请输入产品码: ").strip()

    # 获取加密文件路径
    dat_file = input("请输入dat文件路径 (或按回车跳过): ").strip()
    prd_file = input("请输入prd文件路径 (或按回车跳过): ").strip()

    if not dat_file and not prd_file:
        print("未提供文件路径，使用默认路径...")
        # 尝试常见的路径
        possible_paths = [
            "/Users/<USER>/Desktop/mt6/dat",
            "/Users/<USER>/Desktop/mt6/prd",
            "./dat",
            "./prd"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                if path.endswith('dat'):
                    dat_file = path
                elif path.endswith('prd'):
                    prd_file = path
                print(f"找到文件: {path}")

    # 解密文件
    if dat_file and os.path.exists(dat_file):
        print(f"\n{'='*20} 解密 DAT 文件 {'='*20}")
        decryptor.try_all_combinations(dat_file, auth_code, product_code)
    elif dat_file:
        print(f"DAT文件不存在: {dat_file}")

    if prd_file and os.path.exists(prd_file):
        print(f"\n{'='*20} 解密 PRD 文件 {'='*20}")
        decryptor.try_all_combinations(prd_file, auth_code, product_code)
    elif prd_file:
        print(f"PRD文件不存在: {prd_file}")

    if not dat_file and not prd_file:
        print("未找到要解密的文件！")
        print("请确保dat或prd文件存在，并提供正确的路径。")

if __name__ == "__main__":
    main()
