#!/usr/bin/env python3
"""
智能解密工具 - 尝试多种产品码组合
"""

import hashlib
import base64
import os
import hmac
from struct import pack

class SmartDecryptor:
    def __init__(self):
        self.POSSIBLE_SALTS = [
            b"MtInstaller",
            b"MaiTian", 
            b"salt",
            b"MtInstaller2023",
            b"BeautyBox",
            b"MaiTianFxPlugin"
        ]
        
        self.POSSIBLE_ITERATIONS = [1000, 4096, 10000]
        self.MACHINE_SERIAL = "C02PWRY5FR1M"
        
        # 可能的产品码
        self.POSSIBLE_PRODUCT_CODES = [
            "",  # 空产品码
            "PROD456",  # 默认测试码
            "BeautyBox",
            "MaiTian",
            "MtInstaller", 
            "FxPlug",
            "6.0",
            "BeautyBox6.0",
            "MaiTianFxPlugin",
            "DigitalAnarchy"
        ]
    
    def pbkdf2_simple(self, password, salt, iterations, key_length):
        """PBKDF2密钥派生"""
        def prf(password, salt):
            return hmac.new(password, salt, hashlib.sha256).digest()
        
        def f(password, salt, iterations, i):
            u = prf(password, salt + pack('>I', i))
            result = u
            for _ in range(iterations - 1):
                u = prf(password, u)
                result = bytes(a ^ b for a, b in zip(result, u))
            return result
        
        key = b''
        i = 1
        while len(key) < key_length:
            key += f(password, salt, iterations, i)
            i += 1
        
        return key[:key_length]
    
    def xor_decrypt(self, data, key):
        """XOR解密"""
        result = bytearray()
        key_len = len(key)
        for i, byte in enumerate(data):
            result.append(byte ^ key[i % key_len])
        return bytes(result)
    
    def is_pkg_file(self, data):
        """检查是否是PKG文件"""
        if len(data) < 8:
            return False
        
        # PKG文件的魔数
        pkg_signatures = [
            b'xar!',  # XAR archive (PKG format)
            b'PK\x03\x04',  # ZIP format (some PKG files)
            b'PK\x05\x06',  # ZIP format
            b'PK\x07\x08',  # ZIP format
        ]
        
        for sig in pkg_signatures:
            if data.startswith(sig):
                return True
        
        return False
    
    def analyze_result(self, data, description):
        """分析解密结果"""
        if not data or len(data) < 4:
            return False
        
        print(f"  {description}:")
        print(f"    大小: {len(data)} 字节")
        
        # 显示头部
        header = data[:min(32, len(data))]
        print(f"    头部: {header.hex()}")
        
        # 检查PKG格式
        if self.is_pkg_file(data):
            print(f"    ✅ PKG文件格式！")
            return True
        
        # 检查其他格式
        if data.startswith(b'PK'):
            print(f"    ✅ ZIP/PKG文件格式")
            return True
        elif data.startswith(b'\x7fELF'):
            print(f"    ✅ ELF可执行文件")
            return True
        elif data.startswith(b'\xcf\xfa\xed\xfe') or data.startswith(b'\xfe\xed\xfa\xcf'):
            print(f"    ✅ Mach-O可执行文件")
            return True
        elif data.startswith(b'<?xml'):
            print(f"    ✅ XML文件")
            return True
        elif b'Contents' in data[:100] or b'Info.plist' in data[:100]:
            print(f"    ✅ 可能是应用程序包")
            return True
        else:
            print(f"    ❌ 未识别的格式")
        
        return False
    
    def comprehensive_decrypt(self, dat_path, prd_path, auth_code):
        """全面解密测试"""
        print(f"=== 智能解密测试 ===")
        print(f"授权码: {auth_code}")
        print(f"机器序列号: {self.MACHINE_SERIAL}")
        
        # 读取文件
        with open(dat_path, 'rb') as f:
            dat_raw = f.read()
        
        with open(prd_path, 'rb') as f:
            prd_data = f.read()
        
        # Base64解码dat文件
        try:
            dat_decoded = base64.b64decode(dat_raw)
            print(f"DAT文件: {len(dat_raw)} 字节 -> Base64解码后: {len(dat_decoded)} 字节")
        except:
            dat_decoded = dat_raw
            print(f"DAT文件: {len(dat_raw)} 字节 (非Base64)")
        
        print(f"PRD文件: {len(prd_data)} 字节")
        
        success_count = 0
        
        # 尝试所有产品码组合
        for product_code in self.POSSIBLE_PRODUCT_CODES:
            print(f"\n--- 测试产品码: '{product_code}' ---")
            
            # 尝试不同的盐值和迭代次数
            for salt in self.POSSIBLE_SALTS:
                for iterations in self.POSSIBLE_ITERATIONS:
                    
                    # 生成PBKDF2密钥
                    password = f"{auth_code}{product_code}{self.MACHINE_SERIAL}".encode('utf-8')
                    pbkdf2_key = self.pbkdf2_simple(password, salt, iterations, 32)
                    
                    # 方法1: PBKDF2密钥与PRD数据XOR后作为最终密钥
                    combined_key = self.xor_decrypt(pbkdf2_key, prd_data)
                    result = self.xor_decrypt(dat_decoded, combined_key)
                    
                    if self.analyze_result(result, f"PBKDF2⊕PRD ({salt.decode('utf-8', errors='ignore')}, {iterations})"):
                        success_count += 1
                        self.save_result(dat_path, result, f"smart_{product_code}_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                    
                    # 方法2: 直接使用PBKDF2密钥
                    result2 = self.xor_decrypt(dat_decoded, pbkdf2_key)
                    
                    if self.analyze_result(result2, f"直接PBKDF2 ({salt.decode('utf-8', errors='ignore')}, {iterations})"):
                        success_count += 1
                        self.save_result(dat_path, result2, f"direct_{product_code}_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                    
                    # 如果找到PKG文件就停止
                    if success_count > 0:
                        print(f"\n🎉 找到PKG文件！停止搜索。")
                        return True
        
        print(f"\n总计成功解密: {success_count} 次")
        return success_count > 0
    
    def save_result(self, original_file, data, suffix):
        """保存解密结果"""
        output_file = f"{original_file}.smart_{suffix}"
        try:
            with open(output_file, 'wb') as f:
                f.write(data)
            print(f"    💾 保存到: {output_file}")
        except Exception as e:
            print(f"    ❌ 保存失败: {e}")

def main():
    """主函数"""
    decryptor = SmartDecryptor()
    
    print("MtInstaller 智能解密工具")
    print("尝试多种产品码组合寻找PKG文件")
    print("=" * 50)
    
    # 文件路径
    dat_path = "/Users/<USER>/Desktop/dat"
    prd_path = "/Users/<USER>/Desktop/prd"
    
    # 检查文件是否存在
    if not os.path.exists(dat_path):
        print(f"DAT文件不存在: {dat_path}")
        return
    
    if not os.path.exists(prd_path):
        print(f"PRD文件不存在: {prd_path}")
        return
    
    # 使用提供的激活码
    auth_code = "25f8af52740a429682dbc52a9be0ca48"
    
    print(f"使用激活码: {auth_code}")
    
    # 开始智能解密
    success = decryptor.comprehensive_decrypt(dat_path, prd_path, auth_code)
    
    if success:
        print(f"\n🎉 解密成功！找到了PKG文件。")
    else:
        print(f"\n😞 未找到PKG文件。可能需要:")
        print(f"   1. 不同的产品码")
        print(f"   2. 不同的解密算法")
        print(f"   3. 额外的解密步骤")

if __name__ == "__main__":
    main()
