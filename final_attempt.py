#!/usr/bin/env python3
"""
最终尝试 - 考虑多种可能的解密方式
"""

import base64
import hashlib
import hmac
import os
import zlib
import gzip
from struct import pack, unpack

class FinalDecryptor:
    def __init__(self):
        self.AUTH_CODE = "25f8af52740a429682dbc52a9be0ca48"
        self.MACHINE_SERIAL = "C02PWRY5FR1M"
        
        # 从代码分析中得到的所有可能参数
        self.SALTS = [
            b"MtInstaller",
            b"MaiTian", 
            b"salt",
            b"BeautyBox",
            b"MaiTianFxPlugin"
        ]
        
        self.ITERATIONS = [1000, 4096, 10000]
        
        # 可能的产品码
        self.PRODUCT_CODES = [
            "",
            "BeautyBox",
            "MaiTian",
            "6.0",
            "BeautyBox6.0"
        ]
    
    def pbkdf2_derive(self, password, salt, iterations, key_length):
        """PBKDF2密钥派生"""
        def prf(password, salt):
            return hmac.new(password, salt, hashlib.sha256).digest()
        
        def f(password, salt, iterations, i):
            u = prf(password, salt + pack('>I', i))
            result = u
            for _ in range(iterations - 1):
                u = prf(password, u)
                result = bytes(a ^ b for a, b in zip(result, u))
            return result
        
        key = b''
        i = 1
        while len(key) < key_length:
            key += f(password, salt, iterations, i)
            i += 1
        
        return key[:key_length]
    
    def try_decompress(self, data):
        """尝试解压缩数据"""
        methods = []
        
        # 尝试zlib解压
        try:
            decompressed = zlib.decompress(data)
            methods.append(("zlib", decompressed))
        except:
            pass
        
        # 尝试gzip解压
        try:
            decompressed = gzip.decompress(data)
            methods.append(("gzip", decompressed))
        except:
            pass
        
        # 尝试去掉前几个字节后解压（可能有头部）
        for skip in [1, 2, 4, 8, 16]:
            if len(data) > skip:
                try:
                    decompressed = zlib.decompress(data[skip:])
                    methods.append((f"zlib_skip_{skip}", decompressed))
                except:
                    pass
                
                try:
                    decompressed = gzip.decompress(data[skip:])
                    methods.append((f"gzip_skip_{skip}", decompressed))
                except:
                    pass
        
        return methods
    
    def analyze_data(self, data, description):
        """分析数据"""
        if not data or len(data) < 4:
            return False
        
        print(f"  {description}:")
        print(f"    大小: {len(data)} 字节")
        print(f"    头部: {data[:min(32, len(data))].hex()}")
        
        # 检查文件格式
        success = False
        
        if data.startswith(b'xar!'):
            print(f"    ✅ XAR/PKG文件！")
            success = True
        elif data.startswith(b'PK'):
            print(f"    ✅ ZIP/PKG文件！")
            success = True
        elif data.startswith(b'\x1f\x8b'):
            print(f"    ✅ GZIP压缩文件")
            success = True
        elif data.startswith(b'BZh'):
            print(f"    ✅ BZIP2压缩文件")
            success = True
        elif len(data) > 1000:
            print(f"    ? 大文件，可能是有效数据")
            success = True
        
        # 尝试解压缩
        if not success:
            decompress_results = self.try_decompress(data)
            for method, decompressed in decompress_results:
                if len(decompressed) > len(data) * 2:  # 解压后明显变大
                    print(f"    ✅ {method}解压成功: {len(decompressed)} 字节")
                    success = True
                    # 递归分析解压后的数据
                    self.analyze_data(decompressed, f"{description} -> {method}")
        
        if not success:
            print(f"    ❌ 未识别格式")
        
        return success
    
    def comprehensive_test(self):
        """全面测试"""
        print("=== 最终解密尝试 ===")
        print(f"激活码: {self.AUTH_CODE}")
        print(f"机器序列号: {self.MACHINE_SERIAL}")
        
        # 读取文件
        with open("/Users/<USER>/Desktop/dat", 'rb') as f:
            dat_raw = f.read()
        
        with open("/Users/<USER>/Desktop/prd", 'rb') as f:
            prd_data = f.read()
        
        print(f"DAT原始: {len(dat_raw)} 字节")
        print(f"PRD: {len(prd_data)} 字节")
        
        # Base64解码
        try:
            dat_decoded = base64.b64decode(dat_raw)
            print(f"DAT Base64解码: {len(dat_decoded)} 字节")
        except:
            dat_decoded = dat_raw
            print("DAT不是Base64")
        
        success_count = 0
        
        # 尝试所有组合
        for product_code in self.PRODUCT_CODES:
            print(f"\n--- 产品码: '{product_code}' ---")
            
            for salt in self.SALTS:
                for iterations in self.ITERATIONS:
                    
                    # 生成密钥
                    password = f"{self.AUTH_CODE}{product_code}{self.MACHINE_SERIAL}".encode('utf-8')
                    key = self.pbkdf2_derive(password, salt, iterations, 32)
                    
                    # 方法1: 直接XOR
                    result1 = self.xor_decrypt(dat_decoded, key)
                    if self.analyze_data(result1, f"XOR ({salt.decode('utf-8', errors='ignore')}, {iterations})"):
                        success_count += 1
                        self.save_result(result1, f"final_xor_{product_code}_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                    
                    # 方法2: 与PRD组合
                    combined_key = self.xor_decrypt(key, prd_data)
                    result2 = self.xor_decrypt(dat_decoded, combined_key)
                    if self.analyze_data(result2, f"XOR+PRD ({salt.decode('utf-8', errors='ignore')}, {iterations})"):
                        success_count += 1
                        self.save_result(result2, f"final_xor_prd_{product_code}_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                    
                    # 方法3: 多轮XOR
                    temp_data = dat_decoded
                    for round_num in range(1, 4):
                        temp_data = self.xor_decrypt(temp_data, key)
                        if self.analyze_data(temp_data, f"多轮XOR第{round_num}轮 ({salt.decode('utf-8', errors='ignore')}, {iterations})"):
                            success_count += 1
                            self.save_result(temp_data, f"final_multi_{round_num}_{product_code}_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                    
                    # 如果找到大文件就停止
                    if success_count > 0:
                        print(f"找到 {success_count} 个可能的结果")
        
        print(f"\n总计找到: {success_count} 个可能的结果")
        return success_count > 0
    
    def xor_decrypt(self, data, key):
        """XOR解密"""
        result = bytearray()
        key_len = len(key)
        for i, byte in enumerate(data):
            result.append(byte ^ key[i % key_len])
        return bytes(result)
    
    def save_result(self, data, suffix):
        """保存结果"""
        output_file = f"/Users/<USER>/Desktop/dat.final_{suffix}"
        try:
            with open(output_file, 'wb') as f:
                f.write(data)
            print(f"    💾 保存到: {output_file}")
        except Exception as e:
            print(f"    ❌ 保存失败: {e}")

def main():
    """主函数"""
    decryptor = FinalDecryptor()
    
    print("MtInstaller 最终解密尝试")
    print("考虑压缩、多步解密等可能性")
    print("=" * 50)
    
    success = decryptor.comprehensive_test()
    
    if success:
        print(f"\n🎉 找到了一些可能的结果！")
        print(f"请检查生成的文件，看是否包含PKG文件。")
    else:
        print(f"\n😞 仍未找到PKG文件。")
        print(f"可能的原因:")
        print(f"1. 需要网络下载真正的PKG文件")
        print(f"2. dat文件只是配置信息，不包含PKG")
        print(f"3. 需要其他未知的解密步骤")

if __name__ == "__main__":
    main()
