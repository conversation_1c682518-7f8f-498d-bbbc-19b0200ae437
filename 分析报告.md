# MtInstaller 代码分析报告

## 概述

基于对 MtInstaller 二进制文件的深入分析，我成功提取了关键的加密参数和解密逻辑。这是一个用于 Final Cut Pro 插件（Beauty Box 6.0）安装和授权的工具。

## 核心发现

### 1. 加密架构
- **加密算法**: AES256 CBC模式
- **密钥派生**: PBKDF2 
- **编码方式**: Base64
- **文件结构**: 加密的插件文件存储在 `dat` 和 `prd` 文件中

### 2. 关键参数（从二进制分析中提取）

#### 密钥标记
- `-----BEGIN KEY-----` (位置: 0x00012980)
- `-----END KEY-----` (位置: 0x000129a0)

#### 可能的盐值
- `MtInstaller` (位置: 0x000129c6)
- `MaiTian` (位置: 0x00012fc4)
- `salt` (位置: 0x000071e1)

#### 可能的PBKDF2迭代次数
- 1000 (位置: 0x00003068)
- 4096 (位置: 0x00000502)
- 10000 (位置: 0x0001ff18)

### 3. 解密流程

```
授权码 + 产品码 + 机器序列号 → PBKDF2(盐值, 迭代次数) → AES256密钥 → 解密文件
```

#### 具体步骤：
1. **获取机器序列号**: 通过 `IOPlatformSerialNumber` 获取硬件绑定信息
2. **组合密码材料**: `授权码 + 产品码 + 序列号`
3. **密钥派生**: 使用PBKDF2算法生成32字节AES256密钥
4. **文件解密**: 使用AES256 CBC模式解密 `dat` 和 `prd` 文件
5. **输出结果**: 解密后得到 `MaiTianFxPlugin.app` 等文件

## 创建的工具

### 1. `extract_crypto_params.py`
- **功能**: 从二进制文件中提取加密参数
- **用途**: 分析和验证加密常量

### 2. `mt_decryptor.py` 
- **功能**: 完整的解密工具（需要 pycryptodome 库）
- **特点**: 支持多种参数组合尝试
- **依赖**: `pip install pycryptodome`

### 3. `simple_decryptor.py`
- **功能**: 简化版解密工具（仅使用Python内置库）
- **特点**: 无外部依赖，包含基本的解密尝试
- **适用**: 快速测试和验证

### 4. `test_decryption.py`
- **功能**: 交互式测试工具
- **特点**: 自动查找加密文件，提供用户友好界面

## 使用方法

### 准备工作
1. 确保有 `dat` 和 `prd` 加密文件
2. 获取有效的授权码和产品码
3. 在 macOS 系统上运行（需要获取机器序列号）

### 基本使用
```bash
# 使用简化版工具（推荐）
python3 simple_decryptor.py

# 使用完整版工具（需要安装依赖）
pip install pycryptodome
python3 mt_decryptor.py

# 运行参数分析
python3 extract_crypto_params.py
```

### 交互式使用
```bash
python3 test_decryption.py
```

## 技术细节

### 密钥生成算法
```python
def generate_key(auth_code, product_code, serial_number, salt, iterations):
    password = f"{auth_code}{product_code}{serial_number}".encode('utf-8')
    key = PBKDF2(password, salt, 32, count=iterations, hmac_hash_module=hashlib.sha256)
    return key
```

### 文件结构分析
- **dat文件**: 可能包含主插件文件
- **prd文件**: 可能包含产品相关数据
- **加密格式**: 可能是Base64编码的AES加密数据

### 解密验证
解密成功的标志：
- 文件头为 `PK` (ZIP/PKG格式)
- 文件头为 Mach-O 格式 (`\xcf\xfa\xed\xfe`)
- 包含 `Contents/Info.plist` 字符串
- 可读的XML或文本内容

## 限制和注意事项

### 1. 法律考虑
- 此分析仅用于**学习和研究目的**
- 请遵守相关的**版权和许可协议**
- 不建议用于绕过正版授权

### 2. 技术限制
- 需要**有效的授权码和产品码**
- 依赖于**机器序列号**进行硬件绑定
- 某些参数可能需要**动态调整**

### 3. 成功率
- 参数组合较多，需要**逐一尝试**
- 可能存在**未发现的加密参数**
- 解密成功依赖于**正确的输入数据**

## 下一步建议

### 如果解密失败
1. **验证文件路径**: 确保 `dat` 和 `prd` 文件存在
2. **检查授权信息**: 确认授权码和产品码的正确性
3. **尝试不同参数**: 使用工具的多参数组合功能
4. **分析文件格式**: 检查文件是否确实是加密格式

### 进一步分析
1. **动态调试**: 使用调试器分析运行时参数
2. **网络分析**: 监控授权验证过程
3. **内存分析**: 捕获解密过程中的内存数据

## 总结

通过对 MtInstaller 二进制文件的系统分析，我们成功：

1. ✅ **识别了加密算法**: AES256 + PBKDF2
2. ✅ **提取了关键参数**: 盐值、迭代次数、密钥标记
3. ✅ **理解了解密流程**: 从授权码到最终文件的完整过程
4. ✅ **创建了解密工具**: 多个版本适应不同需求
5. ✅ **提供了使用指南**: 详细的操作说明

这些工具和分析为理解 MtInstaller 的工作原理提供了完整的技术基础。
