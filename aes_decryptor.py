#!/usr/bin/env python3
"""
MtInstaller AES解密工具
使用Python内置的hashlib实现AES解密
"""

import hashlib
import base64
import os
import subprocess
import hmac
from struct import pack, unpack

class AESDecryptor:
    def __init__(self):
        # 基于分析得到的参数
        self.POSSIBLE_SALTS = [
            b"MtInstaller",
            b"MaiTian", 
            b"salt",
            b"MtInstaller2023",
            b"BeautyBox",
            b"MaiTianFxPlugin"
        ]
        
        self.POSSIBLE_ITERATIONS = [1000, 4096, 10000]
        
        # 机器序列号（从之前的分析中获得）
        self.MACHINE_SERIAL = "C02PWRY5FR1M"
    
    def pbkdf2_simple(self, password, salt, iterations, key_length):
        """简化的PBKDF2实现"""
        def prf(password, salt):
            return hmac.new(password, salt, hashlib.sha256).digest()
        
        def f(password, salt, iterations, i):
            u = prf(password, salt + pack('>I', i))
            result = u
            for _ in range(iterations - 1):
                u = prf(password, u)
                result = bytes(a ^ b for a, b in zip(result, u))
            return result
        
        key = b''
        i = 1
        while len(key) < key_length:
            key += f(password, salt, iterations, i)
            i += 1
        
        return key[:key_length]
    
    def aes_decrypt_block(self, key, block):
        """简化的AES解密（仅用于测试）"""
        # 这是一个非常简化的实现，仅用于演示
        # 实际的AES需要完整的轮函数实现
        result = bytearray(16)
        for i in range(16):
            result[i] = block[i] ^ key[i % len(key)]
        return bytes(result)
    
    def try_aes_cbc_decrypt(self, data, key, iv=None):
        """尝试AES CBC解密"""
        if len(data) % 16 != 0:
            return None
        
        if iv is None:
            # 假设IV是数据的前16字节
            if len(data) < 32:
                return None
            iv = data[:16]
            ciphertext = data[16:]
        else:
            ciphertext = data
        
        # 简化的CBC解密
        decrypted = bytearray()
        prev_block = iv
        
        for i in range(0, len(ciphertext), 16):
            block = ciphertext[i:i+16]
            if len(block) != 16:
                break
            
            # 简化的AES解密
            decrypted_block = self.aes_decrypt_block(key, block)
            
            # CBC模式：与前一个密文块XOR
            for j in range(16):
                decrypted.append(decrypted_block[j] ^ prev_block[j])
            
            prev_block = block
        
        return bytes(decrypted)
    
    def remove_pkcs7_padding(self, data):
        """移除PKCS7填充"""
        if len(data) == 0:
            return data
        
        padding_length = data[-1]
        if padding_length > 16 or padding_length > len(data):
            return data
        
        # 验证填充
        for i in range(padding_length):
            if data[-(i+1)] != padding_length:
                return data
        
        return data[:-padding_length]
    
    def analyze_file(self, file_path):
        """分析文件结构"""
        print(f"\n=== 分析文件: {file_path} ===")
        
        with open(file_path, 'rb') as f:
            raw_data = f.read()
        
        print(f"原始文件大小: {len(raw_data)} 字节")
        
        # 尝试Base64解码
        try:
            decoded_data = base64.b64decode(raw_data)
            print(f"Base64解码后大小: {len(decoded_data)} 字节")
            
            # 显示解码后的头部
            header = decoded_data[:32]
            print(f"解码后头部: {header.hex()}")
            
            return decoded_data
        except:
            print("不是Base64格式，使用原始数据")
            return raw_data
    
    def test_decryption(self, file_path, auth_code, product_code):
        """测试解密"""
        print(f"\n开始解密测试")
        print(f"文件: {file_path}")
        print(f"授权码: {auth_code}")
        print(f"产品码: {product_code}")
        print(f"机器序列号: {self.MACHINE_SERIAL}")
        print("=" * 60)
        
        # 分析文件
        encrypted_data = self.analyze_file(file_path)
        
        success_count = 0
        
        # 尝试不同的参数组合
        for salt in self.POSSIBLE_SALTS:
            for iterations in self.POSSIBLE_ITERATIONS:
                print(f"\n测试参数: 盐值={salt.decode('utf-8', errors='ignore')}, 迭代次数={iterations}")
                
                # 生成密钥
                password = f"{auth_code}{product_code}{self.MACHINE_SERIAL}".encode('utf-8')
                key = self.pbkdf2_simple(password, salt, iterations, 32)
                
                print(f"生成的密钥: {key.hex()[:32]}...{key.hex()[-8:]}")
                
                # 尝试不同的解密方法
                
                # 方法1: 假设前16字节是IV
                if len(encrypted_data) >= 32:
                    try:
                        iv = encrypted_data[:16]
                        ciphertext = encrypted_data[16:]
                        
                        decrypted = self.try_aes_cbc_decrypt(ciphertext, key, iv)
                        if decrypted:
                            # 尝试移除填充
                            unpadded = self.remove_pkcs7_padding(decrypted)
                            
                            if self.analyze_decrypted_data(unpadded, "AES-CBC (前16字节作为IV)"):
                                success_count += 1
                                self.save_result(file_path, unpadded, f"aes_iv_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                    except Exception as e:
                        print(f"  AES-CBC解密失败: {e}")
                
                # 方法2: 使用密钥的前16字节作为IV
                try:
                    iv = key[:16]
                    decrypted = self.try_aes_cbc_decrypt(encrypted_data, key, iv)
                    if decrypted:
                        unpadded = self.remove_pkcs7_padding(decrypted)
                        
                        if self.analyze_decrypted_data(unpadded, "AES-CBC (密钥前16字节作为IV)"):
                            success_count += 1
                            self.save_result(file_path, unpadded, f"aes_key_iv_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                except Exception as e:
                    print(f"  AES-CBC(密钥IV)解密失败: {e}")
                
                # 方法3: 使用固定IV (全零)
                try:
                    iv = b'\x00' * 16
                    decrypted = self.try_aes_cbc_decrypt(encrypted_data, key, iv)
                    if decrypted:
                        unpadded = self.remove_pkcs7_padding(decrypted)
                        
                        if self.analyze_decrypted_data(unpadded, "AES-CBC (零IV)"):
                            success_count += 1
                            self.save_result(file_path, unpadded, f"aes_zero_iv_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                except Exception as e:
                    print(f"  AES-CBC(零IV)解密失败: {e}")
                
                # 方法4: 简单XOR（作为对比）
                try:
                    xor_result = bytearray()
                    for i, byte in enumerate(encrypted_data):
                        xor_result.append(byte ^ key[i % len(key)])
                    
                    if self.analyze_decrypted_data(bytes(xor_result), "XOR解密"):
                        success_count += 1
                        self.save_result(file_path, bytes(xor_result), f"xor_{salt.decode('utf-8', errors='ignore')}_{iterations}")
                except Exception as e:
                    print(f"  XOR解密失败: {e}")
        
        print(f"\n总计成功解密: {success_count} 次")
        return success_count > 0
    
    def analyze_decrypted_data(self, data, method_name):
        """分析解密后的数据"""
        if not data or len(data) < 4:
            return False
        
        print(f"  {method_name}:")
        print(f"    大小: {len(data)} 字节")
        
        # 显示前32字节
        header = data[:min(32, len(data))]
        print(f"    头部: {header.hex()}")
        
        # 检查文件类型
        if data.startswith(b'PK'):
            print(f"    ✓ 可能是ZIP/PKG文件")
            return True
        elif data.startswith(b'\x7fELF'):
            print(f"    ✓ 可能是ELF可执行文件")
            return True
        elif data.startswith(b'\xcf\xfa\xed\xfe') or data.startswith(b'\xfe\xed\xfa\xcf'):
            print(f"    ✓ 可能是Mach-O可执行文件")
            return True
        elif data.startswith(b'<?xml'):
            print(f"    ✓ 可能是XML文件")
            return True
        elif b'Contents/Info.plist' in data[:min(1024, len(data))]:
            print(f"    ✓ 可能是macOS应用程序包")
            return True
        else:
            # 检查是否是可读文本
            try:
                text_sample = data[:100].decode('utf-8')
                printable_ratio = len([c for c in text_sample if c.isprintable()]) / len(text_sample)
                if printable_ratio > 0.8:
                    print(f"    ? 可能是文本文件: {text_sample[:30]}...")
                    return True
            except:
                pass
        
        print(f"    ✗ 未识别的格式")
        return False
    
    def save_result(self, original_file, data, suffix):
        """保存解密结果"""
        output_file = f"{original_file}.decrypted_{suffix}"
        try:
            with open(output_file, 'wb') as f:
                f.write(data)
            print(f"    保存到: {output_file}")
        except Exception as e:
            print(f"    保存失败: {e}")

def main():
    """主函数"""
    decryptor = AESDecryptor()
    
    print("MtInstaller AES解密工具")
    print("基于二进制分析，支持AES-CBC解密")
    print("=" * 50)
    
    # 测试文件
    test_files = [
        "/Users/<USER>/Desktop/dat",
        "/Users/<USER>/Desktop/prd"
    ]
    
    # 获取解密参数
    auth_code = input("请输入授权码 (或按回车使用TEST123): ").strip()
    if not auth_code:
        auth_code = "TEST123"
    
    product_code = input("请输入产品码 (或按回车使用PROD456): ").strip()
    if not product_code:
        product_code = "PROD456"
    
    # 测试每个文件
    for file_path in test_files:
        if os.path.exists(file_path):
            decryptor.test_decryption(file_path, auth_code, product_code)
        else:
            print(f"文件不存在: {file_path}")

if __name__ == "__main__":
    main()
