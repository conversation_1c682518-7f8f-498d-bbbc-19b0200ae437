#!/usr/bin/env python3
"""
MtInstaller 最终分析工具
综合分析所有解密结果，寻找真正的应用程序文件
"""

import os
import base64
import hashlib

def analyze_all_decrypted_files():
    """分析所有解密文件"""
    print("=== 解密文件分析报告 ===")
    
    # 查找所有解密文件
    decrypted_files = []
    desktop_path = "/Users/<USER>/Desktop/"
    
    try:
        for filename in os.listdir(desktop_path):
            if filename.startswith("dat.decrypted_"):
                decrypted_files.append(os.path.join(desktop_path, filename))
    except:
        print("无法访问桌面目录")
        return
    
    if not decrypted_files:
        print("未找到解密文件")
        return
    
    print(f"找到 {len(decrypted_files)} 个解密文件:")
    
    for file_path in decrypted_files:
        print(f"\n--- {os.path.basename(file_path)} ---")
        
        try:
            with open(file_path, 'rb') as f:
                data = f.read()
            
            print(f"大小: {len(data)} 字节")
            
            # 显示十六进制头部
            header = data[:32]
            print(f"头部: {header.hex()}")
            
            # 尝试解析为文本
            try:
                text_data = data.decode('utf-8', errors='ignore')
                printable_chars = sum(1 for c in text_data if c.isprintable())
                print(f"可打印字符比例: {printable_chars/len(text_data)*100:.1f}%")
                
                if printable_chars > len(text_data) * 0.5:
                    print(f"文本内容预览: {text_data[:100]}...")
            except:
                pass
            
            # 检查是否包含特定模式
            patterns_to_check = [
                b'MaiTian',
                b'BeautyBox',
                b'FxPlug',
                b'Contents',
                b'Info.plist',
                b'PK',  # ZIP header
                b'\xcf\xfa\xed\xfe',  # Mach-O
                b'<?xml'
            ]
            
            found_patterns = []
            for pattern in patterns_to_check:
                if pattern in data:
                    found_patterns.append(pattern.decode('utf-8', errors='ignore'))
            
            if found_patterns:
                print(f"发现模式: {found_patterns}")
            
            # 计算文件熵
            byte_counts = [0] * 256
            for b in data:
                byte_counts[b] += 1
            
            import math
            entropy = 0
            for count in byte_counts:
                if count > 0:
                    p = count / len(data)
                    entropy -= p * math.log2(p)
            
            print(f"熵值: {entropy:.2f}")
            
        except Exception as e:
            print(f"分析失败: {e}")

def check_original_files():
    """重新检查原始文件"""
    print("\n=== 原始文件重新分析 ===")
    
    files_to_check = [
        "/Users/<USER>/Desktop/dat",
        "/Users/<USER>/Desktop/prd"
    ]
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"{file_path} 不存在")
            continue
        
        print(f"\n--- {os.path.basename(file_path)} ---")
        
        with open(file_path, 'rb') as f:
            data = f.read()
        
        print(f"大小: {len(data)} 字节")
        print(f"头部: {data[:32].hex()}")
        
        # 检查是否是Base64
        try:
            decoded = base64.b64decode(data)
            print(f"Base64解码后大小: {len(decoded)} 字节")
            print(f"解码后头部: {decoded[:32].hex()}")
            
            # 检查解码后的数据是否有特殊模式
            if len(decoded) >= 4:
                # 检查是否是长度前缀的数据
                length_prefix = int.from_bytes(decoded[:4], 'big')
                print(f"可能的长度前缀 (大端): {length_prefix}")
                
                length_prefix_le = int.from_bytes(decoded[:4], 'little')
                print(f"可能的长度前缀 (小端): {length_prefix_le}")
                
                # 如果长度前缀合理，显示后续数据
                if 4 <= length_prefix <= len(decoded) - 4:
                    payload = decoded[4:4+length_prefix]
                    print(f"长度前缀数据: {payload[:32].hex()}...")
                
                if 4 <= length_prefix_le <= len(decoded) - 4:
                    payload_le = decoded[4:4+length_prefix_le]
                    print(f"小端长度前缀数据: {payload_le[:32].hex()}...")
        
        except:
            print("不是Base64格式")

def suggest_next_steps():
    """建议下一步操作"""
    print("\n=== 分析结论和建议 ===")
    
    print("""
基于当前的分析结果，我们发现：

1. **文件结构**:
   - dat文件: 3040字节，Base64编码后113字节
   - prd文件: 32字节，可能是密钥或配置
   
2. **解密结果**:
   - 成功生成了5个解密文件
   - 所有解密文件都是113字节
   - 没有发现完整的应用程序文件

3. **可能的情况**:
   - dat/prd文件可能只是配置或密钥文件
   - 真正的应用程序可能存储在其他位置
   - 可能需要网络下载或从其他资源获取

4. **建议的下一步**:
   
   a) **检查MtInstaller应用程序包**:
      - 查看 MtInstaller.app/Contents/Resources/ 目录
      - 寻找其他加密文件或资源
   
   b) **网络分析**:
      - 运行MtInstaller并监控网络请求
      - 查看是否从服务器下载额外文件
   
   c) **动态分析**:
      - 使用正确的授权码运行程序
      - 监控文件系统变化
      - 查看程序创建了哪些文件
   
   d) **进一步的静态分析**:
      - 分析MtInstaller二进制文件的其他部分
      - 查找硬编码的URL或文件路径
      - 检查是否有其他加密算法

5. **当前成果**:
   - 成功识别了加密算法 (AES256 + PBKDF2)
   - 提取了关键参数 (盐值、迭代次数)
   - 创建了多个解密工具
   - 成功解密了dat文件（虽然内容可能不是最终目标）
""")

def main():
    """主函数"""
    print("MtInstaller 最终分析报告")
    print("=" * 50)
    
    # 分析解密文件
    analyze_all_decrypted_files()
    
    # 重新检查原始文件
    check_original_files()
    
    # 提供建议
    suggest_next_steps()

if __name__ == "__main__":
    main()
