#!/usr/bin/env python3
"""
从 MtInstaller 二进制文件中提取加密参数
"""

import struct
import re
import os

def extract_crypto_params(binary_path):
    """从二进制文件中提取加密参数"""
    
    with open(binary_path, 'rb') as f:
        data = f.read()
    
    print("MtInstaller 加密参数提取工具")
    print("=" * 50)
    
    # 1. 查找 BEGIN KEY 和 END KEY 标记的位置
    begin_key_pos = data.find(b"-----BEGIN KEY-----")
    end_key_pos = data.find(b"-----END KEY-----")
    
    if begin_key_pos != -1 and end_key_pos != -1:
        print(f"找到密钥标记:")
        print(f"  BEGIN KEY 位置: 0x{begin_key_pos:08x}")
        print(f"  END KEY 位置: 0x{end_key_pos:08x}")
        
        # 查看标记周围的数据
        context_start = max(0, begin_key_pos - 64)
        context_end = min(len(data), end_key_pos + 64)
        context_data = data[context_start:context_end]
        
        print(f"\n密钥标记周围的数据:")
        for i in range(0, len(context_data), 16):
            chunk = context_data[i:i+16]
            hex_str = ' '.join(f'{b:02x}' for b in chunk)
            ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
            print(f"  {context_start + i:08x}: {hex_str:<48} |{ascii_str}|")
    
    # 2. 查找可能的数字常量（PBKDF2迭代次数）
    print(f"\n查找可能的迭代次数常量:")
    common_iterations = [1000, 4096, 10000, 100000, 1000000]
    
    for iterations in common_iterations:
        # 查找小端序和大端序
        little_endian = struct.pack('<I', iterations)
        big_endian = struct.pack('>I', iterations)
        
        le_pos = data.find(little_endian)
        be_pos = data.find(big_endian)
        
        if le_pos != -1:
            print(f"  找到 {iterations} (小端序) 在位置: 0x{le_pos:08x}")
        if be_pos != -1:
            print(f"  找到 {iterations} (大端序) 在位置: 0x{be_pos:08x}")
    
    # 3. 查找可能的盐值模式
    print(f"\n查找可能的盐值:")
    
    # 查找固定长度的字符串（可能是盐值）
    salt_patterns = [
        b"MtInstaller",
        b"MaiTian", 
        b"BeautyBox",
        b"salt",
        b"SALT"
    ]
    
    for pattern in salt_patterns:
        pos = data.find(pattern)
        if pos != -1:
            print(f"  找到模式 '{pattern.decode('utf-8', errors='ignore')}' 在位置: 0x{pos:08x}")
            
            # 显示周围的数据
            context_start = max(0, pos - 32)
            context_end = min(len(data), pos + len(pattern) + 32)
            context = data[context_start:context_end]
            
            hex_str = ' '.join(f'{b:02x}' for b in context)
            print(f"    上下文: {hex_str}")
    
    # 4. 查找可能的IV或密钥数据（16或32字节的固定模式）
    print(f"\n查找可能的IV/密钥数据:")
    
    # 查找重复的16字节或32字节模式
    for size in [16, 32]:
        print(f"  查找 {size} 字节的重复模式...")
        
        patterns_found = {}
        for i in range(0, len(data) - size, 4):  # 每4字节检查一次
            pattern = data[i:i+size]
            
            # 跳过全零或全FF的模式
            if pattern == b'\x00' * size or pattern == b'\xff' * size:
                continue
            
            # 检查是否有足够的熵（不是简单重复）
            unique_bytes = len(set(pattern))
            if unique_bytes < size // 4:  # 至少要有1/4的字节是不同的
                continue
            
            if pattern in patterns_found:
                patterns_found[pattern].append(i)
            else:
                patterns_found[pattern] = [i]
        
        # 显示找到的重复模式
        for pattern, positions in patterns_found.items():
            if len(positions) > 1:  # 只显示重复出现的模式
                print(f"    {size}字节模式在位置 {[hex(p) for p in positions[:3]]} 重复出现")
                print(f"    模式: {pattern.hex()}")
    
    # 5. 查找字符串中的可能参数
    print(f"\n查找字符串中的加密相关信息:")
    
    # 提取所有可打印字符串
    strings = re.findall(b'[\x20-\x7e]{8,}', data)
    
    crypto_related = []
    for s in strings:
        s_str = s.decode('utf-8', errors='ignore').lower()
        if any(keyword in s_str for keyword in ['key', 'salt', 'iv', 'pbkdf', 'aes', 'crypto', 'cipher']):
            crypto_related.append(s)
    
    for s in crypto_related[:10]:  # 只显示前10个
        print(f"  {s.decode('utf-8', errors='ignore')}")
    
    return {
        'begin_key_pos': begin_key_pos,
        'end_key_pos': end_key_pos,
        'crypto_strings': crypto_related
    }

def analyze_dat_prd_files(dat_path, prd_path):
    """分析 dat 和 prd 文件的结构"""
    
    print(f"\n分析加密文件结构:")
    print("=" * 30)
    
    for file_path, name in [(dat_path, "dat"), (prd_path, "prd")]:
        if not os.path.exists(file_path):
            print(f"{name} 文件不存在: {file_path}")
            continue
        
        with open(file_path, 'rb') as f:
            data = f.read()
        
        print(f"\n{name} 文件分析:")
        print(f"  文件大小: {len(data)} 字节")
        
        # 显示文件头
        header = data[:64]
        print(f"  文件头 (前64字节):")
        for i in range(0, len(header), 16):
            chunk = header[i:i+16]
            hex_str = ' '.join(f'{b:02x}' for b in chunk)
            ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
            print(f"    {i:04x}: {hex_str:<48} |{ascii_str}|")
        
        # 检查是否是Base64
        try:
            import base64
            decoded = base64.b64decode(data)
            print(f"  可能是Base64编码，解码后大小: {len(decoded)} 字节")
            
            # 显示解码后的头部
            decoded_header = decoded[:32]
            hex_str = ' '.join(f'{b:02x}' for b in decoded_header)
            print(f"  解码后头部: {hex_str}")
            
        except:
            print(f"  不是有效的Base64编码")
        
        # 检查熵（随机性）
        byte_counts = [0] * 256
        for b in data:
            byte_counts[b] += 1
        
        # 计算熵
        import math
        entropy = 0
        for count in byte_counts:
            if count > 0:
                p = count / len(data)
                entropy -= p * math.log2(p)
        
        print(f"  文件熵: {entropy:.2f} (8.0为完全随机)")
        
        if entropy > 7.5:
            print(f"  高熵值，可能是加密数据")
        elif entropy < 4.0:
            print(f"  低熵值，可能是文本或压缩数据")

if __name__ == "__main__":
    binary_path = "/Users/<USER>/Desktop/MtInstaller"
    
    if os.path.exists(binary_path):
        params = extract_crypto_params(binary_path)
    else:
        print(f"二进制文件不存在: {binary_path}")
    
    # 分析dat和prd文件（需要提供实际路径）
    # analyze_dat_prd_files("/path/to/dat", "/path/to/prd")
