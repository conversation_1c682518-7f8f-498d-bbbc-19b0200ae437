#!/usr/bin/env python3
"""
测试 MtInstaller 解密功能
"""

import os
import sys
from mt_decryptor import MtDecryptor

def test_with_sample_data():
    """使用示例数据测试解密功能"""
    
    print("MtInstaller 解密测试")
    print("=" * 40)
    
    decryptor = MtDecryptor()
    
    # 显示分析得到的参数
    print("从二进制分析得到的参数:")
    print(f"  默认盐值: {decryptor.PBKDF2_SALT}")
    print(f"  默认迭代次数: {decryptor.PBKDF2_ITERATIONS}")
    print(f"  可能的盐值: {[s.decode('utf-8', errors='ignore') for s in decryptor.POSSIBLE_SALTS]}")
    print(f"  可能的迭代次数: {decryptor.POSSIBLE_ITERATIONS}")
    
    # 获取机器序列号
    serial = decryptor.get_machine_serial()
    print(f"  机器序列号: {serial}")
    
    print("\n" + "=" * 40)
    
    # 测试密钥生成
    test_auth_code = "TEST123"
    test_product_code = "PROD456"
    
    print(f"测试密钥生成:")
    print(f"  授权码: {test_auth_code}")
    print(f"  产品码: {test_product_code}")
    
    for i, salt in enumerate(decryptor.POSSIBLE_SALTS[:3]):  # 只测试前3个
        for j, iterations in enumerate(decryptor.POSSIBLE_ITERATIONS):
            # 临时设置参数
            decryptor.PBKDF2_SALT = salt
            decryptor.PBKDF2_ITERATIONS = iterations
            
            key = decryptor.generate_key(test_auth_code, test_product_code, serial)
            
            print(f"  盐值={salt.decode('utf-8', errors='ignore')}, 迭代={iterations}")
            print(f"    密钥: {key.hex()[:32]}...{key.hex()[-8:]}")
    
    return True

def find_encrypted_files():
    """查找可能的加密文件"""
    
    print("\n查找加密文件:")
    print("-" * 30)
    
    # 可能的文件位置
    search_paths = [
        "/Users/<USER>/Desktop/mt6/",
        "/Users/<USER>/Desktop/",
        "./",
        "../"
    ]
    
    found_files = []
    
    for base_path in search_paths:
        if not os.path.exists(base_path):
            continue
            
        print(f"搜索路径: {base_path}")
        
        try:
            for item in os.listdir(base_path):
                item_path = os.path.join(base_path, item)
                
                if os.path.isfile(item_path):
                    # 查找可能的加密文件
                    if item.lower() in ['dat', 'prd'] or item.lower().endswith(('.dat', '.prd', '.enc', '.encrypted')):
                        found_files.append(item_path)
                        print(f"  找到可能的加密文件: {item_path}")
                        
                        # 显示文件信息
                        size = os.path.getsize(item_path)
                        print(f"    文件大小: {size} 字节")
                        
                        # 显示文件头
                        try:
                            with open(item_path, 'rb') as f:
                                header = f.read(32)
                            print(f"    文件头: {header.hex()}")
                            
                            # 检查是否可能是Base64
                            try:
                                import base64
                                decoded = base64.b64decode(header)
                                print(f"    Base64解码后: {decoded.hex()}")
                            except:
                                print(f"    不是Base64格式")
                                
                        except Exception as e:
                            print(f"    读取文件头失败: {e}")
                            
        except Exception as e:
            print(f"  搜索失败: {e}")
    
    return found_files

def interactive_test():
    """交互式测试"""
    
    print("\n交互式解密测试")
    print("=" * 40)
    
    # 查找文件
    found_files = find_encrypted_files()
    
    if not found_files:
        print("未找到加密文件！")
        print("请确保dat或prd文件存在于以下位置之一:")
        print("  - /Users/<USER>/Desktop/mt6/")
        print("  - /Users/<USER>/Desktop/")
        print("  - 当前目录")
        return False
    
    print(f"\n找到 {len(found_files)} 个可能的加密文件")
    
    # 选择文件
    if len(found_files) == 1:
        selected_file = found_files[0]
        print(f"自动选择文件: {selected_file}")
    else:
        print("请选择要解密的文件:")
        for i, file_path in enumerate(found_files):
            print(f"  {i+1}. {file_path}")
        
        try:
            choice = int(input("请输入文件编号: ")) - 1
            if 0 <= choice < len(found_files):
                selected_file = found_files[choice]
            else:
                print("无效选择！")
                return False
        except ValueError:
            print("无效输入！")
            return False
    
    # 获取解密参数
    print(f"\n准备解密文件: {selected_file}")
    auth_code = input("请输入授权码 (或按回车使用TEST123): ").strip()
    if not auth_code:
        auth_code = "TEST123"
    
    product_code = input("请输入产品码 (或按回车使用PROD456): ").strip()
    if not product_code:
        product_code = "PROD456"
    
    # 开始解密
    decryptor = MtDecryptor()
    success = decryptor.try_all_combinations(selected_file, auth_code, product_code)
    
    if success:
        print("\n解密完成！请检查生成的解密文件。")
    else:
        print("\n解密失败！可能的原因:")
        print("  1. 授权码或产品码不正确")
        print("  2. 文件不是用预期的算法加密的")
        print("  3. 需要其他参数（如特定的盐值或迭代次数）")
    
    return success

def main():
    """主函数"""
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        # 仅运行参数测试
        test_with_sample_data()
    else:
        # 运行完整的交互式测试
        test_with_sample_data()
        interactive_test()

if __name__ == "__main__":
    main()
