#!/usr/bin/env python3
"""
简化版 MtInstaller 解密工具
使用Python内置库，不依赖外部加密库
"""

import hashlib
import base64
import os
import subprocess
import hmac

class SimpleMtDecryptor:
    def __init__(self):
        # 基于二进制分析得到的参数
        self.POSSIBLE_SALTS = [
            b"MtInstaller",
            b"MaiTian", 
            b"salt",
            b"MtInstaller2023",
            b"BeautyBox",
            b"MaiTianFxPlugin"
        ]
        
        # 可能的迭代次数
        self.POSSIBLE_ITERATIONS = [1000, 4096, 10000]
        
        # 从二进制中发现的标记
        self.BEGIN_KEY_MARKER = b"-----B<PERSON>IN KEY-----"
        self.END_KEY_MARKER = b"-----END KEY-----"
    
    def get_machine_serial(self):
        """获取机器序列号"""
        try:
            result = subprocess.run([
                'system_profiler', 'SPHardwareDataType'
            ], capture_output=True, text=True, timeout=10)
            
            for line in result.stdout.split('\n'):
                if 'Serial Number' in line:
                    return line.split(':')[-1].strip()
        except:
            pass
        
        # 备用方法
        try:
            result = subprocess.run([
                'ioreg', '-l'
            ], capture_output=True, text=True, timeout=10)
            
            for line in result.stdout.split('\n'):
                if 'IOPlatformSerialNumber' in line:
                    # 提取序列号
                    parts = line.split('"')
                    if len(parts) >= 4:
                        return parts[3]
        except:
            pass
        
        return "UNKNOWN_SERIAL"
    
    def pbkdf2_simple(self, password, salt, iterations, key_length):
        """简化的PBKDF2实现"""
        def prf(password, salt):
            return hmac.new(password, salt, hashlib.sha256).digest()
        
        def f(password, salt, iterations, i):
            u = prf(password, salt + i.to_bytes(4, 'big'))
            result = u
            for _ in range(iterations - 1):
                u = prf(password, u)
                result = bytes(a ^ b for a, b in zip(result, u))
            return result
        
        key = b''
        i = 1
        while len(key) < key_length:
            key += f(password, salt, iterations, i)
            i += 1
        
        return key[:key_length]
    
    def generate_key(self, auth_code, product_code, serial_number, salt, iterations):
        """生成解密密钥"""
        # 组合密码材料
        password = f"{auth_code}{product_code}{serial_number}".encode('utf-8')
        
        # 使用简化的PBKDF2派生密钥
        key = self.pbkdf2_simple(password, salt, iterations, 32)  # AES256需要32字节
        
        return key
    
    def analyze_file_structure(self, file_path):
        """分析文件结构"""
        print(f"\n分析文件: {file_path}")
        
        if not os.path.exists(file_path):
            print("  文件不存在")
            return None
        
        with open(file_path, 'rb') as f:
            data = f.read()
        
        print(f"  文件大小: {len(data)} 字节")
        
        # 显示文件头
        header = data[:64] if len(data) >= 64 else data
        print(f"  文件头 (前{len(header)}字节):")
        
        for i in range(0, len(header), 16):
            chunk = header[i:i+16]
            hex_str = ' '.join(f'{b:02x}' for b in chunk)
            ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
            print(f"    {i:04x}: {hex_str:<48} |{ascii_str}|")
        
        # 检查是否是Base64
        is_base64 = False
        decoded_data = None
        
        try:
            # 尝试Base64解码
            decoded_data = base64.b64decode(data)
            is_base64 = True
            print(f"  可能是Base64编码，解码后大小: {len(decoded_data)} 字节")
            
            # 显示解码后的头部
            decoded_header = decoded_data[:32]
            hex_str = ' '.join(f'{b:02x}' for b in decoded_header)
            print(f"  解码后头部: {hex_str}")
            
        except:
            print("  不是有效的Base64编码")
        
        # 计算文件熵
        byte_counts = [0] * 256
        for b in data:
            byte_counts[b] += 1
        
        import math
        entropy = 0
        for count in byte_counts:
            if count > 0:
                p = count / len(data)
                entropy -= p * math.log2(p)
        
        print(f"  文件熵: {entropy:.2f} (8.0为完全随机)")
        
        if entropy > 7.5:
            print("  高熵值，可能是加密数据")
        elif entropy < 4.0:
            print("  低熵值，可能是文本或压缩数据")
        
        return {
            'data': data,
            'is_base64': is_base64,
            'decoded_data': decoded_data,
            'entropy': entropy
        }
    
    def try_simple_xor_decrypt(self, data, key):
        """尝试简单的XOR解密"""
        if len(key) == 0:
            return None
        
        result = bytearray()
        key_len = len(key)
        
        for i, byte in enumerate(data):
            result.append(byte ^ key[i % key_len])
        
        return bytes(result)
    
    def analyze_decrypted_result(self, data, description=""):
        """分析解密结果"""
        if not data or len(data) < 4:
            return False
        
        print(f"    {description}")
        print(f"    解密结果大小: {len(data)} 字节")
        
        # 显示前32字节
        header = data[:32]
        hex_str = ' '.join(f'{b:02x}' for b in header)
        print(f"    头部: {hex_str}")
        
        # 检查常见文件类型
        if data.startswith(b'PK'):
            print("    ✓ 可能是ZIP/PKG文件")
            return True
        elif data.startswith(b'\x7fELF'):
            print("    ✓ 可能是ELF可执行文件")
            return True
        elif data.startswith(b'\xcf\xfa\xed\xfe') or data.startswith(b'\xfe\xed\xfa\xcf'):
            print("    ✓ 可能是Mach-O可执行文件")
            return True
        elif data.startswith(b'<?xml'):
            print("    ✓ 可能是XML文件")
            return True
        elif data.startswith(b'<!DOCTYPE'):
            print("    ✓ 可能是HTML文件")
            return True
        elif b'Contents/Info.plist' in data[:1024]:
            print("    ✓ 可能是macOS应用程序包")
            return True
        else:
            # 检查是否是可读文本
            try:
                text_sample = data[:100].decode('utf-8')
                if len([c for c in text_sample if c.isprintable()]) > len(text_sample) * 0.8:
                    print(f"    ? 可能是文本文件: {text_sample[:50]}...")
                    return True
            except:
                pass
        
        print("    ✗ 未识别的格式")
        return False
    
    def test_all_combinations(self, file_path, auth_code, product_code):
        """测试所有参数组合"""
        print(f"开始解密测试: {file_path}")
        print(f"授权码: {auth_code}")
        print(f"产品码: {product_code}")
        print("=" * 60)
        
        # 分析文件
        file_info = self.analyze_file_structure(file_path)
        if not file_info:
            return False
        
        # 获取机器序列号
        serial_number = self.get_machine_serial()
        print(f"\n机器序列号: {serial_number}")
        
        # 准备测试数据
        test_data = file_info['decoded_data'] if file_info['is_base64'] else file_info['data']
        
        success_count = 0
        
        print(f"\n开始尝试不同的参数组合...")
        print("-" * 40)
        
        # 尝试不同的盐值和迭代次数组合
        for i, salt in enumerate(self.POSSIBLE_SALTS):
            for j, iterations in enumerate(self.POSSIBLE_ITERATIONS):
                print(f"\n[{i+1}/{len(self.POSSIBLE_SALTS)}][{j+1}/{len(self.POSSIBLE_ITERATIONS)}] 测试参数:")
                print(f"  盐值: {salt.decode('utf-8', errors='ignore')}")
                print(f"  迭代次数: {iterations}")
                
                try:
                    # 生成密钥
                    key = self.generate_key(auth_code, product_code, serial_number, salt, iterations)
                    print(f"  生成密钥: {key.hex()[:32]}...{key.hex()[-8:]}")
                    
                    # 尝试不同的解密方法
                    
                    # 方法1: 简单XOR
                    xor_result = self.try_simple_xor_decrypt(test_data, key)
                    if xor_result and self.analyze_decrypted_result(xor_result, "XOR解密结果:"):
                        success_count += 1
                        output_file = f"{file_path}.xor_{salt.decode('utf-8', errors='ignore')}_{iterations}"
                        with open(output_file, 'wb') as f:
                            f.write(xor_result)
                        print(f"    保存到: {output_file}")
                    
                    # 方法2: 使用密钥的不同部分进行XOR
                    for key_part_size in [16, 32]:
                        if len(key) >= key_part_size:
                            key_part = key[:key_part_size]
                            xor_result2 = self.try_simple_xor_decrypt(test_data, key_part)
                            if xor_result2 and self.analyze_decrypted_result(xor_result2, f"XOR解密(密钥前{key_part_size}字节):"):
                                success_count += 1
                                output_file = f"{file_path}.xor{key_part_size}_{salt.decode('utf-8', errors='ignore')}_{iterations}"
                                with open(output_file, 'wb') as f:
                                    f.write(xor_result2)
                                print(f"    保存到: {output_file}")
                    
                except Exception as e:
                    print(f"  ✗ 测试失败: {e}")
        
        print(f"\n测试完成！成功解密: {success_count} 次")
        return success_count > 0

def main():
    """主函数"""
    decryptor = SimpleMtDecryptor()
    
    print("MtInstaller 简化解密工具")
    print("基于二进制分析，使用Python内置库")
    print("=" * 50)
    
    # 查找可能的加密文件
    search_paths = [
        "/Users/<USER>/Desktop/mt6/",
        "/Users/<USER>/Desktop/",
        "./"
    ]
    
    found_files = []
    for base_path in search_paths:
        if os.path.exists(base_path):
            try:
                for item in os.listdir(base_path):
                    item_path = os.path.join(base_path, item)
                    if os.path.isfile(item_path) and item.lower() in ['dat', 'prd']:
                        found_files.append(item_path)
            except:
                pass
    
    if found_files:
        print(f"找到可能的加密文件: {found_files}")
        file_to_test = found_files[0]
    else:
        file_to_test = input("请输入要测试的文件路径: ").strip()
    
    if not os.path.exists(file_to_test):
        print(f"文件不存在: {file_to_test}")
        return
    
    # 获取测试参数
    auth_code = input("请输入授权码 (或按回车使用TEST123): ").strip()
    if not auth_code:
        auth_code = "TEST123"
    
    product_code = input("请输入产品码 (或按回车使用PROD456): ").strip()
    if not product_code:
        product_code = "PROD456"
    
    # 开始测试
    decryptor.test_all_combinations(file_to_test, auth_code, product_code)

if __name__ == "__main__":
    main()
